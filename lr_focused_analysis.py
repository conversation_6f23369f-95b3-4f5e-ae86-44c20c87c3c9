"""
Focused Linear Regression Analysis with Regularization Options
Target Variable: ABI MS Promo Uplift - rel
Features: Only the specified numerical variables
Methods: Linear Regression, Lasso Regression, Ridge Regression
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearReg<PERSON>, Lasso, Ridge
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import statsmodels.api as sm
from sklearn.model_selection import GridSearchCV
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("="*60)
print("FOCUSED LINEAR REGRESSION ANALYSIS WITH REGULARIZATION")
print("="*60)

# Load the dataset
print("\nLoading dataset...")
df = pd.read_csv('Cleaned_Latest_Ads.csv')
print(f"Dataset shape: {df.shape}")

# Define the target variable
target_variable = 'ABI MS Promo Uplift - rel'

# Define the specific features requested
requested_features = [
    'ABI Coverage',
    'Same Week',
    '1 wk after',
    '2 wk after', 
    '1 wk before',
    '2 wk before',
    'Avg Temp',
    'KSM',
    'ABI Promo PTC vs Base',
    'ABI Promo PTC/HL',
    'ABI_Promo_W_Num_Distribution',
    'ABI Promo PTC/HL Index'
]

# Check which features are available in the dataset
print(f"\nChecking availability of requested features:")
available_features = []
missing_features = []

for feature in requested_features:
    if feature in df.columns:
        available_features.append(feature)
        print(f"Available: {feature}")
    else:
        missing_features.append(feature)
        print(f"Missing: {feature} - NOT FOUND")

print(f"\nAvailable features: {len(available_features)}")
print(f"Missing features: {len(missing_features)}")

if missing_features:
    print(f"\nMissing features: {missing_features}")

# Use only available features
feature_columns = available_features
X = df[feature_columns].copy()
y = df[target_variable].copy()

print(f"\nFinal feature matrix shape: {X.shape}")
print(f"Target variable shape: {y.shape}")
print(f"Selected features: {feature_columns}")

# Check for missing values
print(f"\nMissing values analysis:")
missing_info = X.isnull().sum()
if missing_info.sum() > 0:
    print(missing_info[missing_info > 0])
    X = X.fillna(X.median())
    print("Missing values filled with median.")
else:
    print("No missing values found.")

# Basic statistics
print(f"\nTarget variable statistics:")
print(y.describe())

# Split the data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

print(f"\nData split:")
print(f"Training samples: {len(X_train)}")
print(f"Test samples: {len(X_test)}")

# Scale the features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Dictionary to store all models and results
models = {}
results_summary = []

print("\n" + "="*60)
print("1. LINEAR REGRESSION (BASELINE)")
print("="*60)

# Train the Linear Regression model
lr_model = LinearRegression()
lr_model.fit(X_train_scaled, y_train)

# Make predictions
y_train_pred_lr = lr_model.predict(X_train_scaled)
y_test_pred_lr = lr_model.predict(X_test_scaled)

# Calculate performance metrics
train_r2_lr = r2_score(y_train, y_train_pred_lr)
test_r2_lr = r2_score(y_test, y_test_pred_lr)
train_rmse_lr = np.sqrt(mean_squared_error(y_train, y_train_pred_lr))
test_rmse_lr = np.sqrt(mean_squared_error(y_test, y_test_pred_lr))
train_mae_lr = mean_absolute_error(y_train, y_train_pred_lr)
test_mae_lr = mean_absolute_error(y_test, y_test_pred_lr)

# Cross-validation
cv_scores_lr = cross_val_score(lr_model, X_train_scaled, y_train, cv=5, scoring='r2')

print(f"PERFORMANCE METRICS:")
print(f"Training R²: {train_r2_lr:.4f}")
print(f"Test R²: {test_r2_lr:.4f}")
print(f"Training RMSE: {train_rmse_lr:.4f}")
print(f"Test RMSE: {test_rmse_lr:.4f}")
print(f"Cross-validation R² (mean ± std): {cv_scores_lr.mean():.4f} ± {cv_scores_lr.std():.4f}")

# OLS Analysis for Linear Regression
print(f"\nOLS STATISTICAL ANALYSIS:")
X_train_sm = sm.add_constant(X_train_scaled)
ols_model_lr = sm.OLS(y_train, X_train_sm).fit()
print(ols_model_lr.summary())

# Store results
models['Linear Regression'] = {
    'model': lr_model,
    'predictions_train': y_train_pred_lr,
    'predictions_test': y_test_pred_lr,
    'ols_model': ols_model_lr
}

results_summary.append({
    'Method': 'Linear Regression',
    'Train R²': train_r2_lr,
    'Test R²': test_r2_lr,
    'Train RMSE': train_rmse_lr,
    'Test RMSE': test_rmse_lr,
    'CV R² Mean': cv_scores_lr.mean(),
    'CV R² Std': cv_scores_lr.std()
})

print("\n" + "="*60)
print("2. RIDGE REGRESSION (L2 REGULARIZATION)")
print("="*60)

# Hyperparameter tuning for Ridge
ridge_alphas = np.logspace(-3, 3, 20)
ridge_grid = GridSearchCV(Ridge(), {'alpha': ridge_alphas}, cv=5, scoring='r2')
ridge_grid.fit(X_train_scaled, y_train)
best_alpha_ridge = ridge_grid.best_params_['alpha']

print(f"Best Ridge alpha: {best_alpha_ridge:.4f}")

# Train Ridge model with best alpha
ridge_model = Ridge(alpha=best_alpha_ridge)
ridge_model.fit(X_train_scaled, y_train)

# Make predictions
y_train_pred_ridge = ridge_model.predict(X_train_scaled)
y_test_pred_ridge = ridge_model.predict(X_test_scaled)

# Calculate performance metrics
train_r2_ridge = r2_score(y_train, y_train_pred_ridge)
test_r2_ridge = r2_score(y_test, y_test_pred_ridge)
train_rmse_ridge = np.sqrt(mean_squared_error(y_train, y_train_pred_ridge))
test_rmse_ridge = np.sqrt(mean_squared_error(y_test, y_test_pred_ridge))
train_mae_ridge = mean_absolute_error(y_train, y_train_pred_ridge)
test_mae_ridge = mean_absolute_error(y_test, y_test_pred_ridge)

# Cross-validation
cv_scores_ridge = cross_val_score(ridge_model, X_train_scaled, y_train, cv=5, scoring='r2')

print(f"PERFORMANCE METRICS:")
print(f"Training R²: {train_r2_ridge:.4f}")
print(f"Test R²: {test_r2_ridge:.4f}")
print(f"Training RMSE: {train_rmse_ridge:.4f}")
print(f"Test RMSE: {test_rmse_ridge:.4f}")
print(f"Cross-validation R² (mean ± std): {cv_scores_ridge.mean():.4f} ± {cv_scores_ridge.std():.4f}")

# OLS-style analysis for Ridge (using Ridge coefficients)
print(f"\nRIDGE REGRESSION COEFFICIENTS ANALYSIS:")
ridge_coef_df = pd.DataFrame({
    'Feature': feature_columns,
    'Coefficient': ridge_model.coef_,
    'Abs_Coefficient': np.abs(ridge_model.coef_)
}).sort_values('Abs_Coefficient', ascending=False)

print(ridge_coef_df.to_string(index=False))

# Store results
models['Ridge Regression'] = {
    'model': ridge_model,
    'predictions_train': y_train_pred_ridge,
    'predictions_test': y_test_pred_ridge,
    'alpha': best_alpha_ridge
}

results_summary.append({
    'Method': 'Ridge Regression',
    'Train R²': train_r2_ridge,
    'Test R²': test_r2_ridge,
    'Train RMSE': train_rmse_ridge,
    'Test RMSE': test_rmse_ridge,
    'CV R² Mean': cv_scores_ridge.mean(),
    'CV R² Std': cv_scores_ridge.std()
})

print("\n" + "="*60)
print("3. LASSO REGRESSION (L1 REGULARIZATION)")
print("="*60)

# Hyperparameter tuning for Lasso
lasso_alphas = np.logspace(-3, 1, 20)
lasso_grid = GridSearchCV(Lasso(max_iter=2000), {'alpha': lasso_alphas}, cv=5, scoring='r2')
lasso_grid.fit(X_train_scaled, y_train)
best_alpha_lasso = lasso_grid.best_params_['alpha']

print(f"Best Lasso alpha: {best_alpha_lasso:.4f}")

# Train Lasso model with best alpha
lasso_model = Lasso(alpha=best_alpha_lasso, max_iter=2000)
lasso_model.fit(X_train_scaled, y_train)

# Make predictions
y_train_pred_lasso = lasso_model.predict(X_train_scaled)
y_test_pred_lasso = lasso_model.predict(X_test_scaled)

# Calculate performance metrics
train_r2_lasso = r2_score(y_train, y_train_pred_lasso)
test_r2_lasso = r2_score(y_test, y_test_pred_lasso)
train_rmse_lasso = np.sqrt(mean_squared_error(y_train, y_train_pred_lasso))
test_rmse_lasso = np.sqrt(mean_squared_error(y_test, y_test_pred_lasso))
train_mae_lasso = mean_absolute_error(y_train, y_train_pred_lasso)
test_mae_lasso = mean_absolute_error(y_test, y_test_pred_lasso)

# Cross-validation
cv_scores_lasso = cross_val_score(lasso_model, X_train_scaled, y_train, cv=5, scoring='r2')

print(f"PERFORMANCE METRICS:")
print(f"Training R²: {train_r2_lasso:.4f}")
print(f"Test R²: {test_r2_lasso:.4f}")
print(f"Training RMSE: {train_rmse_lasso:.4f}")
print(f"Test RMSE: {test_rmse_lasso:.4f}")
print(f"Cross-validation R² (mean ± std): {cv_scores_lasso.mean():.4f} ± {cv_scores_lasso.std():.4f}")

# Feature selection analysis for Lasso
print(f"\nLASSO FEATURE SELECTION ANALYSIS:")
lasso_coef_df = pd.DataFrame({
    'Feature': feature_columns,
    'Coefficient': lasso_model.coef_,
    'Abs_Coefficient': np.abs(lasso_model.coef_)
}).sort_values('Abs_Coefficient', ascending=False)

selected_features = lasso_coef_df[lasso_coef_df['Coefficient'] != 0]
print(f"Features selected by Lasso: {len(selected_features)} out of {len(feature_columns)}")
print("\nSelected features:")
print(selected_features.to_string(index=False))

if len(selected_features) < len(feature_columns):
    eliminated_features = lasso_coef_df[lasso_coef_df['Coefficient'] == 0]
    print(f"\nFeatures eliminated by Lasso: {len(eliminated_features)}")
    print(eliminated_features['Feature'].tolist())

# Store results
models['Lasso Regression'] = {
    'model': lasso_model,
    'predictions_train': y_train_pred_lasso,
    'predictions_test': y_test_pred_lasso,
    'alpha': best_alpha_lasso,
    'selected_features': len(selected_features)
}

results_summary.append({
    'Method': 'Lasso Regression',
    'Train R²': train_r2_lasso,
    'Test R²': test_r2_lasso,
    'Train RMSE': train_rmse_lasso,
    'Test RMSE': test_rmse_lasso,
    'CV R² Mean': cv_scores_lasso.mean(),
    'CV R² Std': cv_scores_lasso.std()
})

# Summary comparison
print("\n" + "="*60)
print("MODEL COMPARISON SUMMARY")
print("="*60)

comparison_df = pd.DataFrame(results_summary)
print(comparison_df.round(4).to_string(index=False))

# Determine best model
best_test_r2 = max([r['Test R²'] for r in results_summary])
best_model_name = [r['Method'] for r in results_summary if r['Test R²'] == best_test_r2][0]
print(f"\nBest performing model (Test R²): {best_model_name}")

# Generate visualizations
print(f"\nGenerating comprehensive visualizations...")

# 1. Model comparison plot
plt.figure(figsize=(20, 15))

# Actual vs Predicted for all models
for i, (model_name, model_data) in enumerate(models.items()):
    plt.subplot(3, 3, i*3 + 1)
    plt.scatter(y_train, model_data['predictions_train'], alpha=0.6, s=50, color='blue')
    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    train_r2 = r2_score(y_train, model_data['predictions_train'])
    plt.title(f'{model_name} - Training\nR² = {train_r2:.3f}')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(3, 3, i*3 + 2)
    plt.scatter(y_test, model_data['predictions_test'], alpha=0.6, s=50, color='green')
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    test_r2 = r2_score(y_test, model_data['predictions_test'])
    plt.title(f'{model_name} - Test\nR² = {test_r2:.3f}')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(3, 3, i*3 + 3)
    residuals = y_test - model_data['predictions_test']
    plt.hist(residuals, bins=20, alpha=0.7, edgecolor='black')
    plt.xlabel('Residuals')
    plt.ylabel('Frequency')
    plt.title(f'{model_name} - Residuals')
    plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('all_models_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# 2. Performance comparison bar plot
plt.figure(figsize=(15, 10))

methods = [r['Method'] for r in results_summary]
train_r2s = [r['Train R²'] for r in results_summary]
test_r2s = [r['Test R²'] for r in results_summary]
cv_means = [r['CV R² Mean'] for r in results_summary]

x = np.arange(len(methods))
width = 0.25

plt.subplot(2, 2, 1)
plt.bar(x - width, train_r2s, width, label='Train R²', alpha=0.8)
plt.bar(x, test_r2s, width, label='Test R²', alpha=0.8)
plt.bar(x + width, cv_means, width, label='CV R² Mean', alpha=0.8)
plt.xlabel('Method')
plt.ylabel('R² Score')
plt.title('R² Comparison Across Methods')
plt.xticks(x, methods, rotation=45)
plt.legend()
plt.grid(True, alpha=0.3)

# RMSE comparison
train_rmses = [r['Train RMSE'] for r in results_summary]
test_rmses = [r['Test RMSE'] for r in results_summary]

plt.subplot(2, 2, 2)
plt.bar(x - width/2, train_rmses, width, label='Train RMSE', alpha=0.8)
plt.bar(x + width/2, test_rmses, width, label='Test RMSE', alpha=0.8)
plt.xlabel('Method')
plt.ylabel('RMSE')
plt.title('RMSE Comparison Across Methods')
plt.xticks(x, methods, rotation=45)
plt.legend()
plt.grid(True, alpha=0.3)

# Cross-validation scores
plt.subplot(2, 2, 3)
cv_stds = [r['CV R² Std'] for r in results_summary]
plt.bar(x, cv_means, yerr=cv_stds, capsize=5, alpha=0.8)
plt.xlabel('Method')
plt.ylabel('CV R² Score')
plt.title('Cross-Validation R² (with std)')
plt.xticks(x, methods, rotation=45)
plt.grid(True, alpha=0.3)

# Feature coefficients comparison
plt.subplot(2, 2, 4)
lr_coefs = lr_model.coef_
ridge_coefs = ridge_model.coef_
lasso_coefs = lasso_model.coef_

coef_comparison = pd.DataFrame({
    'Feature': feature_columns,
    'Linear': lr_coefs,
    'Ridge': ridge_coefs,
    'Lasso': lasso_coefs
})

# Plot top 5 features by absolute linear regression coefficient
top_features = np.argsort(np.abs(lr_coefs))[-5:]
for i, feature_idx in enumerate(top_features):
    feature_name = feature_columns[feature_idx]
    plt.scatter(0, lr_coefs[feature_idx], s=100, label=f'LR - {feature_name}' if i < 3 else '', alpha=0.7)
    plt.scatter(1, ridge_coefs[feature_idx], s=100, alpha=0.7)
    plt.scatter(2, lasso_coefs[feature_idx], s=100, alpha=0.7)

plt.xlabel('Method')
plt.ylabel('Coefficient Value')
plt.title('Top 5 Feature Coefficients')
plt.xticks([0, 1, 2], ['Linear', 'Ridge', 'Lasso'])
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# Save comprehensive results
with open('comprehensive_model_results.txt', 'w') as f:
    f.write("COMPREHENSIVE LINEAR REGRESSION ANALYSIS\n")
    f.write("="*60 + "\n\n")
    f.write(f"Target: {target_variable}\n")
    f.write(f"Features: {len(feature_columns)}\n")
    f.write(f"Training samples: {len(X_train)}\n")
    f.write(f"Test samples: {len(X_test)}\n\n")
    
    f.write("MODEL COMPARISON:\n")
    f.write(comparison_df.round(4).to_string(index=False))
    f.write(f"\n\nBest Model: {best_model_name}\n\n")
    
    f.write("REGULARIZATION PARAMETERS:\n")
    f.write(f"Ridge alpha: {best_alpha_ridge:.4f}\n")
    f.write(f"Lasso alpha: {best_alpha_lasso:.4f}\n")
    f.write(f"Lasso selected features: {models['Lasso Regression']['selected_features']}/{len(feature_columns)}\n\n")
    
    f.write("FEATURE ANALYSIS:\n")
    f.write("Linear Regression Coefficients:\n")
    feature_importance_lr = pd.DataFrame({
        'Feature': feature_columns,
        'Coefficient': lr_model.coef_,
        'Abs_Coefficient': np.abs(lr_model.coef_)
    }).sort_values('Abs_Coefficient', ascending=False)
    f.write(feature_importance_lr.to_string(index=False))
    f.write("\n\nRidge Regression Coefficients:\n")
    f.write(ridge_coef_df.to_string(index=False))
    f.write("\n\nLasso Regression Coefficients (Selected Features Only):\n")
    f.write(selected_features.to_string(index=False))

print(f"\nFiles generated:")
print(f"- all_models_comparison.png")
print(f"- model_performance_comparison.png") 
print(f"- comprehensive_model_results.txt")

print(f"\n{'='*60}")
print("FINAL SUMMARY")
print(f"{'='*60}")
print(f"Best performing model: {best_model_name}")
print(f"Best test R²: {best_test_r2:.4f}")

if 'Lasso' in best_model_name:
    print(f"Lasso selected {models['Lasso Regression']['selected_features']} out of {len(feature_columns)} features")

# Overfitting analysis
for result in results_summary:
    method = result['Method']
    train_r2 = result['Train R²']
    test_r2 = result['Test R²']
    overfitting_score = train_r2 - test_r2
    print(f"{method}: Overfitting score = {overfitting_score:.4f}")
    
print(f"\nAnalysis completed!") 