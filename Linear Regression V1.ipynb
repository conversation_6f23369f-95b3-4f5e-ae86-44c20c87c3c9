{"cells": [{"cell_type": "code", "execution_count": 19, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1b88ac49-1b3b-452a-b4ca-c6b8d3f13183", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully for Iteration 1 analysis!\n"]}], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "import statsmodels.api as sm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully for Iteration 1 analysis!\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Linear Regression Analysis - Focused Approach\n", "\n", "## Objective\n", "Build a Linear Regression model using only the specified features to predict **ABI MS Promo Uplift - rel**.\n", "\n", "## Selected Features\n", "- ABI Coverage  \n", "- Same Week, 1 wk after, 2 wk after, 1 wk before, 2 wk before\n", "- Avg <PERSON><PERSON>\n", "- KSM  \n", "- ABI Promo PTC vs Base\n", "- ABI Promo PTC/HL\n", "- ABI_Promo_W_Num_Distribution\n", "- ABI Promo PTC/HL Index\n", "\n", "## Target Variable\n", "- **ABI MS Promo Uplift - rel** (Relative promotional uplift)\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "FOCUSED LINEAR REGRESSION ANALYSIS\n", "============================================================\n", "\n", "Loading dataset...\n", "Dataset shape: (752, 36)\n", "\n", "Checking availability of requested features:\n", "✓ ABI Coverage\n", "✓ Same Week\n", "✓ 1 wk after\n", "✓ 2 wk after\n", "✓ 1 wk before\n", "✓ 2 wk before\n", "✓ Avg Temp\n", "✓ KSM\n", "✓ ABI Promo PTC vs Base\n", "✓ ABI Promo PTC/HL\n", "✓ ABI_Promo_W_Num_Distribution\n", "✓ ABI Promo PTC/HL Index\n", "\n", "Available features: 12\n", "Missing features: 0\n", "\n", "Final feature matrix shape: (752, 12)\n", "Target variable shape: (752,)\n", "Selected features: ['ABI Coverage', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before', 'Avg Temp', 'KSM', 'ABI Promo PTC vs Base', 'ABI Promo PTC/HL', 'ABI_Promo_W_Num_Distribution', 'ABI Promo PTC/HL Index']\n"]}], "source": ["# Data Loading and Feature Selection\n", "print(\"=\"*60)\n", "print(\"FOCUSED LINEAR REGRESSION ANALYSIS\")\n", "print(\"=\"*60)\n", "\n", "# Load the dataset\n", "print(\"\\nLoading dataset...\")\n", "df = pd.read_csv('Cleaned_Latest_Ads.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "\n", "# Define the target variable\n", "target_variable = 'ABI MS Promo Uplift - rel'\n", "\n", "# Define the specific features requested\n", "requested_features = [\n", "    'ABI Coverage',\n", "    'Same Week',\n", "    '1 wk after',\n", "    '2 wk after', \n", "    '1 wk before',\n", "    '2 wk before',\n", "    'Avg Temp',\n", "    'KSM',\n", "    'ABI Promo PTC vs Base',\n", "    'ABI Promo PTC/HL',\n", "    'ABI_Promo_W_Num_Distribution',\n", "    'ABI Promo PTC/HL Index'\n", "]\n", "\n", "# Check which features are available in the dataset\n", "print(f\"\\nChecking availability of requested features:\")\n", "available_features = []\n", "missing_features = []\n", "\n", "for feature in requested_features:\n", "    if feature in df.columns:\n", "        available_features.append(feature)\n", "        print(f\"✓ {feature}\")\n", "    else:\n", "        missing_features.append(feature)\n", "        print(f\"✗ {feature} - NOT FOUND\")\n", "\n", "print(f\"\\nAvailable features: {len(available_features)}\")\n", "print(f\"Missing features: {len(missing_features)}\")\n", "\n", "if missing_features:\n", "    print(f\"\\nMissing features: {missing_features}\")\n", "\n", "# Use only available features\n", "feature_columns = available_features\n", "X = df[feature_columns].copy()\n", "y = df[target_variable].copy()\n", "\n", "print(f\"\\nFinal feature matrix shape: {X.shape}\")\n", "print(f\"Target variable shape: {y.shape}\")\n", "print(f\"Selected features: {feature_columns}\")\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values analysis:\n", "ABI Promo PTC/HL Index    66\n", "dtype: int64\n", "Missing values filled with median.\n", "\n", "Target variable statistics:\n", "count    752.000000\n", "mean       3.324146\n", "std        4.003007\n", "min        0.635801\n", "25%        1.648068\n", "50%        2.512538\n", "75%        3.774047\n", "max       68.992889\n", "Name: ABI MS Promo Uplift - rel, dtype: float64\n", "\n", "Feature correlation with target:\n", "ABI Promo PTC vs Base         : -0.356\n", "ABI Promo PTC/HL Index        : -0.158\n", "ABI Coverage                  :  0.151\n", "ABI Promo PTC/HL              : -0.140\n", "Same Week                     :  0.097\n", "ABI_Promo_W_Num_Distribution  : -0.094\n", "Avg Temp                      :  0.055\n", "2 wk after                    :  0.041\n", "1 wk before                   :  0.026\n", "1 wk after                    :  0.018\n", "2 wk before                   :  0.014\n", "KSM                           : -0.009\n", "\n", "Target variable range: 0.64 to 68.99\n", "Mean: 3.32, Median: 2.51\n", "Standard deviation: 4.00\n"]}], "source": ["# Data Preprocessing and Exploration\n", "\n", "# Check for missing values\n", "print(f\"Missing values analysis:\")\n", "missing_info = X.isnull().sum()\n", "if missing_info.sum() > 0:\n", "    print(missing_info[missing_info > 0])\n", "    X = <PERSON>.fillna(X.median())\n", "    print(\"Missing values filled with median.\")\n", "else:\n", "    print(\"No missing values found.\")\n", "\n", "# Basic statistics\n", "print(f\"\\nTarget variable statistics:\")\n", "print(y.describe())\n", "\n", "print(f\"\\nFeature correlation with target:\")\n", "correlations = X.corrwith(y).sort_values(key=abs, ascending=False)\n", "for feature, corr in correlations.items():\n", "    print(f\"{feature:30}: {corr:6.3f}\")\n", "\n", "# Data distribution\n", "print(f\"\\nTarget variable range: {y.min():.2f} to {y.max():.2f}\")\n", "print(f\"Mean: {y.mean():.2f}, Median: {y.median():.2f}\")\n", "print(f\"Standard deviation: {y.std():.2f}\")\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data split:\n", "Training samples: 601\n", "Test samples: 151\n", "\n", "Training Linear Regression Model...\n", "\n", "MODEL PERFORMANCE:\n", "Training R²: 0.1576\n", "Test R²: -0.0488\n", "Training RMSE: 3.9586\n", "Test RMSE: 2.4049\n", "Training MAE: 1.8088\n", "Test MAE: 1.7140\n", "\n", "Model Intercept: 3.4093\n"]}], "source": ["# Model Training and Evaluation\n", "\n", "# Split the data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")\n", "\n", "print(f\"Data split:\")\n", "print(f\"Training samples: {len(X_train)}\")\n", "print(f\"Test samples: {len(X_test)}\")\n", "\n", "# Scale the features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Train the model\n", "print(f\"\\nTraining Linear Regression Model...\")\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_scaled, y_train)\n", "\n", "# Make predictions\n", "y_train_pred = lr_model.predict(X_train_scaled)\n", "y_test_pred = lr_model.predict(X_test_scaled)\n", "\n", "# Calculate performance metrics\n", "train_r2 = r2_score(y_train, y_train_pred)\n", "test_r2 = r2_score(y_test, y_test_pred)\n", "train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))\n", "test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))\n", "train_mae = mean_absolute_error(y_train, y_train_pred)\n", "test_mae = mean_absolute_error(y_test, y_test_pred)\n", "\n", "print(f\"\\nMODEL PERFORMANCE:\")\n", "print(f\"Training R²: {train_r2:.4f}\")\n", "print(f\"Test R²: {test_r2:.4f}\")\n", "print(f\"Training RMSE: {train_rmse:.4f}\")\n", "print(f\"Test RMSE: {test_rmse:.4f}\")\n", "print(f\"Training MAE: {train_mae:.4f}\")\n", "print(f\"Test MAE: {test_mae:.4f}\")\n", "\n", "# Model intercept\n", "print(f\"\\nModel Intercept: {lr_model.intercept_:.4f}\")\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FEATURE IMPORTANCE (sorted by absolute coefficient):\n", "                     Feature  Coefficient  Abs_Coefficient\n", "       ABI Promo PTC vs Base    -1.565411         1.565411\n", "                ABI Coverage     0.508836         0.508836\n", "                   Same Week     0.246887         0.246887\n", "                  2 wk after     0.167453         0.167453\n", "                  1 wk after     0.134162         0.134162\n", "                 2 wk before     0.119855         0.119855\n", "                 1 wk before     0.086595         0.086595\n", "ABI_Promo_W_Num_Distribution     0.084673         0.084673\n", "                         KSM     0.072356         0.072356\n", "      ABI Promo PTC/HL Index     0.061440         0.061440\n", "                    Avg Temp     0.058642         0.058642\n", "            ABI Promo PTC/HL     0.004383         0.004383\n", "\n", "Key Insights:\n", "• Most influential feature: ABI Promo PTC vs Base (coef: -1.565)\n", "• Second most influential: ABI Coverage (coef: 0.509)\n", "\n", "Positive impact features (11):\n", "  • ABI Coverage: +0.509\n", "  • Same Week: +0.247\n", "  • 2 wk after: +0.167\n", "  • 1 wk after: +0.134\n", "  • 2 wk before: +0.120\n", "  • 1 wk before: +0.087\n", "  • ABI_Promo_W_Num_Distribution: +0.085\n", "  • KSM: +0.072\n", "  • ABI Promo PTC/HL Index: +0.061\n", "  • Avg Temp: +0.059\n", "  • ABI Promo PTC/HL: +0.004\n", "\n", "Negative impact features (1):\n", "  • ABI Promo PTC vs Base: -1.565\n"]}], "source": ["# Feature Importance Analysis\n", "\n", "# Feature importance\n", "feature_importance = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': lr_model.coef_,\n", "    'Abs_Coefficient': np.abs(lr_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(f\"FEATURE IMPORTANCE (sorted by absolute coefficient):\")\n", "print(feature_importance.to_string(index=False))\n", "\n", "print(f\"\\nKey Insights:\")\n", "print(f\"• Most influential feature: {feature_importance.iloc[0]['Feature']} (coef: {feature_importance.iloc[0]['Coefficient']:.3f})\")\n", "print(f\"• Second most influential: {feature_importance.iloc[1]['Feature']} (coef: {feature_importance.iloc[1]['Coefficient']:.3f})\")\n", "\n", "# Positive vs negative impact features\n", "positive_features = feature_importance[feature_importance['Coefficient'] > 0]\n", "negative_features = feature_importance[feature_importance['Coefficient'] < 0]\n", "\n", "print(f\"\\nPositive impact features ({len(positive_features)}):\")\n", "for _, row in positive_features.iterrows():\n", "    print(f\"  • {row['Feature']}: +{row['Coefficient']:.3f}\")\n", "    \n", "print(f\"\\nNegative impact features ({len(negative_features)}):\")\n", "for _, row in negative_features.iterrows():\n", "    print(f\"  • {row['Feature']}: {row['Coefficient']:.3f}\")\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Detailed OLS Analysis:\n", "                                OLS Regression Results                               \n", "=====================================================================================\n", "Dep. Variable:     ABI MS Promo Uplift - rel   R-squared:                       0.158\n", "Model:                                   OLS   Adj. R-squared:                  0.140\n", "Method:                        Least Squares   F-statistic:                     9.167\n", "Date:                       Fri, 27 Jun 2025   Prob (F-statistic):           2.60e-16\n", "Time:                               02:33:03   Log-Likelihood:                -1679.7\n", "No. Observations:                        601   AIC:                             3385.\n", "Df Residuals:                            588   BIC:                             3443.\n", "Df Model:                                 12                                         \n", "Covariance Type:                   nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          3.4093      0.163     20.884      0.000       3.089       3.730\n", "x1             0.5088      0.171      2.972      0.003       0.173       0.845\n", "x2             0.2469      0.199      1.241      0.215      -0.144       0.638\n", "x3             0.1342      0.187      0.718      0.473      -0.233       0.501\n", "x4             0.1675      0.171      0.977      0.329      -0.169       0.504\n", "x5             0.0866      0.187      0.462      0.644      -0.282       0.455\n", "x6             0.1199      0.171      0.699      0.485      -0.217       0.457\n", "x7             0.0586      0.177      0.332      0.740      -0.289       0.406\n", "x8             0.0724      0.169      0.428      0.669      -0.260       0.405\n", "x9            -1.5654      0.206     -7.599      0.000      -1.970      -1.161\n", "x10            0.0044      0.189      0.023      0.981      -0.367       0.375\n", "x11            0.0847      0.189      0.448      0.654      -0.287       0.456\n", "x12            0.0614      0.197      0.311      0.756      -0.326       0.449\n", "==============================================================================\n", "Omnibus:                      935.805   <PERSON><PERSON><PERSON>-Watson:                   2.023\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):           346277.919\n", "Skew:                           8.832   Prob(JB):                         0.00\n", "Kurtosis:                     119.259   Cond. No.                         2.24\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\n", "STATISTICAL SIGNIFICANCE ANALYSIS:\n", "Features with p-value < 0.05 (statistically significant):\n", "  ✓ ABI Coverage: p = 0.0031\n", "  ✓ ABI Promo PTC vs Base: p = 0.0000\n", "\n", "Features with p-value >= 0.05 (not statistically significant):\n", "  ✗ Same Week: p = 0.2151\n", "  ✗ 1 wk after: p = 0.4727\n", "  ✗ 2 wk after: p = 0.3288\n", "  ✗ 1 wk before: p = 0.6443\n", "  ✗ 2 wk before: p = 0.4849\n", "  ✗ Avg Temp: p = 0.7402\n", "  ✗ KSM: p = 0.6692\n", "  ✗ ABI Promo PTC/HL: p = 0.9815\n", "  ✗ ABI_Promo_W_Num_Distribution: p = 0.6545\n", "  ✗ ABI Promo PTC/HL Index: p = 0.7556\n", "\n", "MODEL DIAGNOSTICS:\n", "Adjusted R²: 0.1404\n", "F-statistic: 9.17\n", "F-statistic p-value: 2.60e-16\n", "Condition Number: 2.24\n"]}], "source": ["# Detailed Statistical Analysis (OLS)\n", "\n", "# Detailed statistics with statsmodels\n", "print(f\"Detailed OLS Analysis:\")\n", "X_train_sm = sm.add_constant(X_train_scaled)\n", "ols_model = sm.OLS(y_train, X_train_sm).fit()\n", "print(ols_model.summary())\n", "\n", "# Statistical significance analysis\n", "print(f\"\\nSTATISTICAL SIGNIFICANCE ANALYSIS:\")\n", "print(f\"Features with p-value < 0.05 (statistically significant):\")\n", "\n", "# Extract p-values (skip the constant term)\n", "pvalues = ols_model.pvalues[1:]  # Skip intercept\n", "for i, (feature, pval) in enumerate(zip(feature_columns, pvalues)):\n", "    if pval < 0.05:\n", "        print(f\"  {feature}: p = {pval:.4f}\")\n", "\n", "print(f\"\\nFeatures with p-value >= 0.05 (not statistically significant):\")\n", "for i, (feature, pval) in enumerate(zip(feature_columns, pvalues)):\n", "    if pval >= 0.05:\n", "        print(f\"  {feature}: p = {pval:.4f}\")\n", "\n", "# Model diagnostics\n", "print(f\"\\nMODEL DIAGNOSTICS:\")\n", "print(f\"Adjusted R²: {ols_model.rsquared_adj:.4f}\")\n", "print(f\"F-statistic: {ols_model.fvalue:.2f}\")\n", "print(f\"F-statistic p-value: {ols_model.f_pvalue:.2e}\")\n", "print(f\"Condition Number: {ols_model.condition_number:.2f}\")\n", "\n", "if ols_model.condition_number > 30:\n", "    print(\" Warning: High condition number suggests multicollinearity\")\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"image/png": "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***************************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Visualization saved as 'lr_model_performance.png'\n"]}], "source": ["# Model Visualizations\n", "\n", "# 1. Actual vs Predicted plots\n", "plt.figure(figsize=(15, 5))\n", "\n", "plt.subplot(1, 3, 1)\n", "plt.scatter(y_train, y_train_pred, alpha=0.6, s=50, color='blue')\n", "plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)\n", "plt.xlabel('Actual ABI MS Promo Uplift - rel')\n", "plt.ylabel('Predicted ABI MS Promo Uplift - rel')\n", "plt.title(f'Training Set\\nR² = {train_r2:.3f}')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.scatter(y_test, y_test_pred, alpha=0.6, s=50, color='green')\n", "plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "plt.xlabel('Actual ABI MS Promo Uplift - rel')\n", "plt.ylabel('Predicted ABI MS Promo Uplift - rel')\n", "plt.title(f'Test Set\\nR² = {test_r2:.3f}')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 3, 3)\n", "residuals_test = y_test - y_test_pred\n", "plt.hist(residuals_test, bins=20, alpha=0.7, color='green', edgecolor='black')\n", "plt.xlabel('Residuals')\n", "plt.ylabel('Frequency')\n", "plt.title('Test Residuals Distribution')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('lr_model_performance.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"Visualization saved as 'lr_model_performance.png'\")\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Feature importance plot saved as 'feature_importance_visualization.png'\n"]}], "source": ["# Feature Importance Visualization\n", "\n", "plt.figure(figsize=(12, 8))\n", "colors = ['red' if coef < 0 else 'blue' for coef in feature_importance['Coefficient']]\n", "bars = plt.barh(range(len(feature_importance)), feature_importance['Abs_Coefficient'], color=colors, alpha=0.7)\n", "plt.yticks(range(len(feature_importance)), feature_importance['Feature'])\n", "plt.xlabel('Absolute Coefficient Value')\n", "plt.title('Feature Importance (Linear Regression Coefficients)\\nBlue = Positive Impact, Red = Negative Impact')\n", "plt.grid(True, alpha=0.3, axis='x')\n", "\n", "# Add value labels on bars\n", "for i, (bar, coef) in enumerate(zip(bars, feature_importance['Coefficient'])):\n", "    plt.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, \n", "             f'{coef:.3f}', va='center', ha='left', fontsize=10)\n", "\n", "plt.tight_layout()\n", "plt.savefig('feature_importance_visualization.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"Feature importance plot saved as 'feature_importance_visualization.png'\")\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 2}, "notebookName": "Linear Regression V1", "widgets": {}}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 0}