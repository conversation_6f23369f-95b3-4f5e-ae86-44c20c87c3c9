Creating notebooks and Linear Regression Models to predict ABI MS Promo Uplift - rel.
The input columns need to be from the following - ABI Coverage, 
Same Week
1 wk after
2 wk after
1 wk before
2 wk before
Avg Temp
KSM
ABI Promo PTC vs Base
ABI Promo PTC/HL
ABI_Promo_W_Num_Distribution
ABI Promo PTC/HL Index
ABI_Promo_W_W_Distribution


our target Column is ABI MS Promo Uplift - rel

Try different combinations, see what is the best combination to get the best R2 score.
Handle outliers, check for categorical columns, "null" as values etc and account for them.
